"""
Veloera AI API Gateway 的身份验证中间件。

该模块为 OpenAI 兼容的端点提供 API key 身份验证和验证的中间件。
"""

from typing import Optional, Tu<PERSON>
from fastapi import Request, HTTPException, status, Depends
from fastapi.security.utils import get_authorization_scheme_param
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import security
from app.core.logging import get_logger
from app.models.user import User
from app.models.token import Token
from app.services.token_service import TokenService

logger = get_logger(__name__)


class APIKeyAuthMiddleware:
    """用于 API key 身份验证的中间件。"""
    
    def __init__(self):
        self.token_service = None
    
    async def authenticate_api_key(
        self,
        request: Request,
        db: AsyncSession
    ) -> Tuple[Optional[User], Optional[Token], str]:
        """
        从请求头中验证 API key。
        
        返回:
            Tuple[Optional[User], Optional[Token], str]: (user, token, error_message)
        """
        
        # 获取 Authorization 请求头
        authorization = request.headers.get("Authorization")
        if not authorization:
            return None, None, "Missing Authorization header"
        
        # 解析 Authorization 请求头
        scheme, credentials = get_authorization_scheme_param(authorization)
        if scheme.lower() != "bearer":
            return None, None, "Invalid authorization scheme. Use 'Bearer <token>'"
        
        if not credentials:
            return None, None, "Missing API key"
        
        # 验证 API key 格式
        if not security.validate_api_key_format(credentials):
            return None, None, "Invalid API key format"
        
        # 获取客户端 IP 地址
        client_ip = self._get_client_ip(request)
        
        # 从请求中获取模型（用于验证）
        model = await self._extract_model_from_request(request)

        # 对于模型列表端点，跳过模型特定验证
        if request.url.path in ["/v1/models", "/models"]:
            model = None

        # 初始化 TokenService（每次请求都使用新的数据库会话）
        self.token_service = TokenService(db)

        # 验证 token
        token, is_valid, message = await self.token_service.validate_token_access(
            credentials, client_ip, model
        )
        
        if not is_valid:
            logger.warning(
                "API key authentication failed",
                api_key=credentials[:10] + "...",
                client_ip=client_ip,
                model=model,
                reason=message
            )
            return None, None, message
        
        # 获取用户
        user = token.user if token else None
        
        if not user:
            return None, None, "User not found"
        
        if not user.is_active():
            return None, None, "User account is disabled"
        
        logger.debug(f"API密钥认证成功 - 用户ID: {user.id}, 令牌ID: {token.id}, 客户端IP: {client_ip}, 模型: {model}")
        
        return user, token, ""
    
    def _get_client_ip(self, request: Request) -> str:
        """从请求中提取客户端 IP 地址。"""
        
        # 检查转发的头部（当位于代理之后时）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # 取链中的第一个 IP
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接客户端 IP
        return request.client.host if request.client else "unknown"
    
    async def _extract_model_from_request(self, request: Request) -> Optional[str]:
        """从请求体中提取模型名称。"""
        
        try:
            # 仅尝试为 POST 请求解析 JSON
            if request.method != "POST":
                return None
            
            # 检查内容类型是否为 JSON
            content_type = request.headers.get("content-type", "")
            if "application/json" not in content_type:
                return None
            
            # 获取请求体
            body = await request.body()
            if not body:
                return None
            
            # 解析 JSON 以提取模型
            import json
            try:
                data = json.loads(body)
                return data.get("model")
            except json.JSONDecodeError:
                return None
                
        except Exception as e:
            logger.debug(f"从请求中提取模型失败: {str(e)}")
            return None


# 全局中间件实例
api_key_auth = APIKeyAuthMiddleware()


async def get_current_api_user(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Tuple[User, Token]:
    """
    用于从 API key 获取当前已验证用户和 token 的依赖项。

    这应该用于需要 API key 身份验证的 OpenAI 兼容端点。
    """

    user, token, error = await api_key_auth.authenticate_api_key(request, db)

    if not user or not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=error or "Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 将用户和 token 存储在请求状态中以供后续使用
    request.state.user = user
    request.state.token = token

    return user, token


async def get_optional_api_user(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Tuple[Optional[User], Optional[Token]]:
    """
    如果提供了 API key，则用于获取当前用户和 token 的依赖项（可选身份验证）。

    这可用于支持或不支持身份验证的端点。
    """

    try:
        user, token, _ = await api_key_auth.authenticate_api_key(request, db)

        # 如果已验证，则存储在请求状态中
        if user and token:
            request.state.user = user
            request.state.token = token

        return user, token

    except Exception as e:
        logger.debug(f"可选API认证失败: {str(e)}")
        return None, None


def require_api_key(request: Request) -> None:
    """
    要求 API key 身份验证的中间件函数。
    
    这可以用作路由处理程序中的依赖项。
    """
    
    authorization = request.headers.get("Authorization")
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    scheme, credentials = get_authorization_scheme_param(authorization)
    if scheme.lower() != "bearer" or not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key format",
            headers={"WWW-Authenticate": "Bearer"},
        )


def extract_api_key(request: Request) -> Optional[str]:
    """
    从请求头中提取 API key。
    
    返回:
        Optional[str]: 如果存在则为 API key，否则为 None
    """
    
    authorization = request.headers.get("Authorization")
    if not authorization:
        return None
    
    scheme, credentials = get_authorization_scheme_param(authorization)
    if scheme.lower() != "bearer":
        return None
    
    return credentials


def mask_api_key(api_key: str) -> str:
    """
    为日志记录目的屏蔽 API key。
    
    参数:
        api_key: 完整的 API key
        
    返回:
        str: 已屏蔽的 API key
    """
    
    if not api_key or len(api_key) < 8:
        return "****"
    
    return api_key[:8] + "*" * (len(api_key) - 12) + api_key[-4:]
