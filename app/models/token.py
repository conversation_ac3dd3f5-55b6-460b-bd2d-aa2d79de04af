"""
Veloera AI API Gateway 的 Token 模型。

该模块定义了用于管理 API token 的 Token 模型，具有
额度限制、模型限制和 IP 白名单功能。
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
import json

from app.core.database import Base


class Token(Base):
    """用于 API key 管理和访问控制的 Token 模型。"""
    
    __tablename__ = "tokens"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Token 标识
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    key = Column(String(48), unique=True, nullable=False)  # API token key
    name = Column(String(255), index=True)  # Token 名称/描述
    
    # Token 状态和配置
    status = Column(Integer, default=1)  # 1-活动, 2-禁用, 3-过期, 4-耗尽
    group = Column(String(64))  # 用于定价的 Token 组
    
    # 额度管理
    remain_quota = Column(Integer, default=0)  # 剩余额度
    used_quota = Column(Integer, default=0)  # 已用额度
    unlimited_quota = Column(Boolean, default=False)  # 无限额度标志
    
    # 模型限制
    model_limits_enabled = Column(Boolean, default=False)  # 启用模型限制
    model_limits = Column(String(1024))  # 允许的模型 (JSON 数组)
    
    # 访问控制
    allow_ips = Column(Text)  # IP 白名单 (JSON 数组)
    expired_time = Column(Integer, default=-1)  # 过期时间戳 (-1 = 永不)
    
    # 使用情况跟踪
    request_count = Column(Integer, default=0)  # 总请求数
    last_used_time = Column(DateTime)  # 上次使用时间
    
    # 时间戳
    created_time = Column(DateTime, default=lambda: datetime.now(timezone.utc).replace(tzinfo=None), index=True)
    accessed_time = Column(DateTime, default=lambda: datetime.now(timezone.utc).replace(tzinfo=None))
    updated_time = Column(DateTime, default=lambda: datetime.now(timezone.utc).replace(tzinfo=None), onupdate=lambda: datetime.now(timezone.utc).replace(tzinfo=None))
    
    # 关系
    user = relationship("User", back_populates="tokens")
    logs = relationship("Log", back_populates="token")
    transcription_tasks = relationship("TranscriptionTask", back_populates="token")
    
    # 用于性能的索引
    __table_args__ = (
        Index('idx_tokens_key', 'key'),
        Index('idx_tokens_user_id', 'user_id'),
        Index('idx_tokens_status', 'status'),
        Index('idx_tokens_group', 'group'),
        Index('idx_tokens_created_time', 'created_time'),
    )
    
    def __repr__(self) -> str:
        return f"<Token(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """将 Token 转换为字典表示。"""
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "status": self.status,
            "group": self.group,
            "remain_quota": self.remain_quota,
            "used_quota": self.used_quota,
            "unlimited_quota": self.unlimited_quota,
            "model_limits_enabled": self.model_limits_enabled,
            "model_limits": self.get_model_limits(),
            "allow_ips": self.get_allowed_ips(),
            "expired_time": self.expired_time,
            "request_count": self.request_count,
            "last_used_time": self.last_used_time.isoformat() if self.last_used_time else None,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "accessed_time": self.accessed_time.isoformat() if self.accessed_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
        }
        
        if include_sensitive:
            data["key"] = self.key
        else:
            # 为安全起见，屏蔽 Token key
            data["key"] = self._mask_token_key()
        
        return data
    
    def get_model_limits(self) -> List[str]:
        """以列表形式获取允许的模型。"""
        if not self.model_limits:
            return []
        try:
            return json.loads(self.model_limits)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_model_limits(self, models: List[str]) -> None:
        """从列表设置允许的模型。"""
        self.model_limits = json.dumps(models)
    
    def add_model_limit(self, model: str) -> None:
        """向允许的模型中添加一个模型。"""
        models = self.get_model_limits()
        if model not in models:
            models.append(model)
            self.set_model_limits(models)
    
    def remove_model_limit(self, model: str) -> None:
        """从允许的模型中移除一个模型。"""
        models = self.get_model_limits()
        if model in models:
            models.remove(model)
            self.set_model_limits(models)
    
    def can_use_model(self, model: str) -> bool:
        """检查 Token 是否可以使用特定模型。"""
        if not self.model_limits_enabled:
            return True
        return model in self.get_model_limits()
    
    def get_allowed_ips(self) -> List[str]:
        """以列表形式获取允许的 IP 地址。"""
        if not self.allow_ips:
            return []
        try:
            return json.loads(self.allow_ips)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_allowed_ips(self, ips: List[str]) -> None:
        """从列表设置允许的 IP 地址。"""
        self.allow_ips = json.dumps(ips)
    
    def add_allowed_ip(self, ip: str) -> None:
        """向允许的 IP 地址中添加一个 IP 地址。"""
        ips = self.get_allowed_ips()
        if ip not in ips:
            ips.append(ip)
            self.set_allowed_ips(ips)
    
    def remove_allowed_ip(self, ip: str) -> None:
        """从允许的 IP 地址中移除一个 IP 地址。"""
        ips = self.get_allowed_ips()
        if ip in ips:
            ips.remove(ip)
            self.set_allowed_ips(ips)
    
    def is_ip_allowed(self, ip: str) -> bool:
        """检查 IP 地址是否被允许。"""
        allowed_ips = self.get_allowed_ips()
        if not allowed_ips:
            return True  # 无 IP 限制
        return ip in allowed_ips
    
    def is_active(self) -> bool:
        """检查 Token 是否处于活动状态。"""
        return self.status == 1
    
    def is_expired(self) -> bool:
        """检查 Token 是否已过期。"""
        if self.expired_time == -1:
            return False  # 永不过期
        return datetime.now(timezone.utc).timestamp() > self.expired_time
    
    def has_sufficient_quota(self, required_quota: int) -> bool:
        """检查 Token 是否有足够的额度。"""
        if self.unlimited_quota:
            return True
        return self.remain_quota >= required_quota
    
    def consume_quota(self, amount: int) -> bool:
        """消耗 Token 额度。"""
        if self.unlimited_quota:
            self.used_quota += amount
            return True
        
        if not self.has_sufficient_quota(amount):
            return False
        
        self.remain_quota -= amount
        self.used_quota += amount
        
        # 检查额度是否已耗尽
        if self.remain_quota <= 0:
            self.status = 4  # 已耗尽
        
        return True
    
    def add_quota(self, amount: int) -> None:
        """向 Token 添加额度。"""
        if not self.unlimited_quota:
            self.remain_quota += amount
            
            # 如果 Token 已耗尽，则重新激活
            if self.status == 4 and self.remain_quota > 0:
                self.status = 1
    
    def increment_request_count(self) -> None:
        """增加 Token 请求计数。"""
        self.request_count += 1
        self.last_used_time = datetime.now(timezone.utc).replace(tzinfo=None)
    
    def update_access_time(self) -> None:
        """更新 Token 访问时间。"""
        self.accessed_time = datetime.now(timezone.utc).replace(tzinfo=None)
    
    def _mask_token_key(self) -> str:
        """为安全起见，屏蔽 Token key。"""
        if not self.key or len(self.key) < 8:
            return "****"
        return self.key[:8] + "*" * (len(self.key) - 12) + self.key[-4:]
    
    def activate(self) -> None:
        """激活 Token。"""
        if not self.is_expired() and (self.unlimited_quota or self.remain_quota > 0):
            self.status = 1
    
    def deactivate(self) -> None:
        """停用 Token。"""
        self.status = 2
    
    def set_expiration(self, timestamp: int) -> None:
        """设置 Token 过期时间戳。"""
        self.expired_time = timestamp
        
        # 如果时间戳已过，则标记为已过期
        if self.is_expired():
            self.status = 3
    
    def remove_expiration(self) -> None:
        """移除 Token 过期设置。"""
        self.expired_time = -1
        
        # 如果未耗尽，则重新激活
        if self.status == 3 and (self.unlimited_quota or self.remain_quota > 0):
            self.status = 1
    
    def get_status_text(self) -> str:
        """获取人类可读的状态文本。"""
        status_map = {
            1: "Active",
            2: "Disabled",
            3: "Expired",
            4: "Exhausted"
        }
        return status_map.get(self.status, "Unknown")
    
    def validate_access(self, ip_address: str, model: Optional[str]) -> tuple[bool, str]:
        """验证给定 IP 和模型的 Token 访问权限。"""
        if not self.is_active():
            return False, f"Token is {self.get_status_text().lower()}"

        if self.is_expired():
            self.status = 3
            return False, "Token has expired"

        if not self.is_ip_allowed(ip_address):
            return False, "IP address not allowed"

        # 跳过模型验证，如果模型为 None（例如，对于模型列表端点）
        if model is not None and not self.can_use_model(model):
            return False, f"Model '{model}' not allowed for this token"

        return True, "访问已授予"
