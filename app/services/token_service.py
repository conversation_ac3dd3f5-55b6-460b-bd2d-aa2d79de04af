"""
Veloera AI API Gateway 的令牌服务。

该模块提供令牌管理业务逻辑，包括
令牌 CRUD 操作、配额管理和访问控制。
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_

from app.models.token import Token
from app.models.user import User
from app.core.security import security
from app.core.logging import get_logger

logger = get_logger(__name__)


class TokenService:
    """用于令牌管理操作的服务类。"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def get_token_by_id(self, token_id: int) -> Optional[Token]:
        """按 ID 获取令牌。"""
        result = await self.db.execute(
            select(Token).where(Token.id == token_id)
        )
        return result.scalar_one_or_none()
    
    async def get_token_by_key(self, token_key: str) -> Optional[Token]:
        """按 API 密钥获取令牌。"""
        from sqlalchemy.orm import selectinload
        result = await self.db.execute(
            select(Token)
            .options(selectinload(Token.user))
            .where(Token.key == token_key)
        )
        return result.scalar_one_or_none()
    
    async def get_user_tokens(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        status: Optional[int] = None
    ) -> List[Token]:
        """获取特定用户的令牌。"""
        
        query = select(Token).where(Token.user_id == user_id)
        
        if status is not None:
            query = query.where(Token.status == status)
        
        query = query.offset(skip).limit(limit)
        query = query.order_by(Token.created_time.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_tokens(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        user_id: Optional[int] = None,
        status: Optional[int] = None,
        group: Optional[str] = None
    ) -> List[Token]:
        """获取具有筛选和分页功能的令牌。"""
        
        query = select(Token)
        
        # 应用筛选器
        if search:
            query = query.where(Token.name.ilike(f"%{search}%"))
        
        if user_id is not None:
            query = query.where(Token.user_id == user_id)
        
        if status is not None:
            query = query.where(Token.status == status)
        
        if group:
            query = query.where(Token.group == group)
        
        # 应用分页和排序
        query = query.offset(skip).limit(limit)
        query = query.order_by(Token.created_time.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_token_count(
        self,
        search: Optional[str] = None,
        user_id: Optional[int] = None,
        status: Optional[int] = None,
        group: Optional[str] = None
    ) -> int:
        """获取具有筛选器的令牌总数。"""
        
        query = select(func.count(Token.id))
        
        # 应用与 get_tokens 相同的筛选器
        if search:
            query = query.where(Token.name.ilike(f"%{search}%"))
        
        if user_id is not None:
            query = query.where(Token.user_id == user_id)
        
        if status is not None:
            query = query.where(Token.status == status)
        
        if group:
            query = query.where(Token.group == group)
        
        result = await self.db.execute(query)
        return result.scalar()
    
    async def create_token(
        self,
        user_id: int,
        name: str,
        remain_quota: int = 0,
        unlimited_quota: bool = False,
        model_limits_enabled: bool = False,
        model_limits: Optional[List[str]] = None,
        allow_ips: Optional[List[str]] = None,
        expired_time: int = -1,
        group: Optional[str] = None
    ) -> Token:
        """创建一个新令牌。"""
        
        # 生成 API 密钥
        api_key = security.generate_api_key(user_id)
        
        token = Token(
            user_id=user_id,
            key=api_key,
            name=name,
            status=1,  # 默认激活
            remain_quota=remain_quota,
            unlimited_quota=unlimited_quota,
            model_limits_enabled=model_limits_enabled,
            expired_time=expired_time,
            group=group
        )
        
        # 如果提供了模型限制，则设置模型限制
        if model_limits:
            token.set_model_limits(model_limits)
        
        # 如果提供了允许的 IP，则设置允许的 IP
        if allow_ips:
            token.set_allowed_ips(allow_ips)
        
        self.db.add(token)
        await self.db.commit()
        await self.db.refresh(token)
        
        logger.info(f"令牌创建成功 - ID: {token.id}, 用户ID: {user_id}, 名称: {name}, 无限配额: {unlimited_quota}")
        
        return token
    
    async def update_token(
        self,
        token_id: int,
        **update_data
    ) -> Optional[Token]:
        """更新令牌信息。"""
        
        # 处理模型限制
        if "model_limits" in update_data and update_data["model_limits"] is not None:
            model_limits = update_data.pop("model_limits")
            token = await self.get_token_by_id(token_id)
            if token:
                token.set_model_limits(model_limits)
        
        # 处理允许的 IP
        if "allow_ips" in update_data and update_data["allow_ips"] is not None:
            allow_ips = update_data.pop("allow_ips")
            token = await self.get_token_by_id(token_id)
            if token:
                token.set_allowed_ips(allow_ips)
        
        # 删除 None 值
        filtered_data = {k: v for k, v in update_data.items() if v is not None}
        filtered_data["updated_time"] = datetime.now(timezone.utc).replace(tzinfo=None)
        
        if filtered_data:
            await self.db.execute(
                update(Token)
                .where(Token.id == token_id)
                .values(**filtered_data)
            )
        
        await self.db.commit()
        
        # 返回更新后的令牌
        updated_token = await self.get_token_by_id(token_id)
        
        logger.info(f"令牌更新成功 - ID: {token_id}, 更新字段: {list(filtered_data.keys())}")
        
        return updated_token
    
    async def delete_token(self, token_id: int) -> bool:
        """删除令牌（通过设置状态进行软删除）。"""
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(status=2, updated_time=datetime.now(timezone.utc).replace(tzinfo=None))  # 2 = 已禁用
        )
        await self.db.commit()
        
        logger.info(f"令牌删除成功 - ID: {token_id}")
        return True
    
    async def add_quota(self, token_id: int, amount: int) -> bool:
        """向令牌添加配额。"""
        
        token = await self.get_token_by_id(token_id)
        if not token:
            return False
        
        if token.unlimited_quota:
            # 无法向无限配额的令牌添加配额
            return False
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(
                remain_quota=Token.remain_quota + amount,
                updated_time=datetime.now(timezone.utc).replace(tzinfo=None)
            )
        )
        await self.db.commit()
        
        # 如果令牌已用尽，则重新激活令牌
        if token.status == 4:  # 已用尽
            await self.update_token(token_id, status=1)
        
        logger.info(f"令牌配额增加成功 - ID: {token_id}, 增加量: {amount}")
        
        return True
    
    async def consume_quota(self, token_id: int, amount: int) -> bool:
        """从令牌消耗配额。"""
        
        token = await self.get_token_by_id(token_id)
        if not token:
            return False
        
        if token.unlimited_quota:
            # 更新已用配额以进行跟踪
            await self.db.execute(
                update(Token)
                .where(Token.id == token_id)
                .values(
                    used_quota=Token.used_quota + amount,
                    request_count=Token.request_count + 1,
                    last_used_time=datetime.now(timezone.utc).replace(tzinfo=None),
                    updated_time=datetime.now(timezone.utc).replace(tzinfo=None)
                )
            )
            await self.db.commit()
            return True
        
        if not token.has_sufficient_quota(amount):
            return False
        
        # 计算新的配额值
        new_remain_quota = token.remain_quota - amount
        new_used_quota = token.used_quota + amount
        new_status = token.status
        
        # 如果配额已用尽，则标记为已用尽
        if new_remain_quota <= 0:
            new_status = 4  # 已用尽
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(
                remain_quota=new_remain_quota,
                used_quota=new_used_quota,
                status=new_status,
                request_count=Token.request_count + 1,
                last_used_time=datetime.now(timezone.utc).replace(tzinfo=None),
                updated_time=datetime.now(timezone.utc).replace(tzinfo=None)
            )
        )
        await self.db.commit()
        
        logger.debug(f"令牌配额消费成功 - ID: {token_id}, 消费量: {amount}, 剩余: {new_remain_quota}")
        
        return True
    
    async def validate_token_access(
        self,
        token_key: str,
        ip_address: str,
        model: Optional[str]
    ) -> tuple[Optional[Token], bool, str]:
        """验证 API 请求的令牌访问权限。"""

        # 按密钥获取令牌
        token = await self.get_token_by_key(token_key)
        if not token:
            return None, False, "Invalid token"

        # 验证令牌访问权限
        is_valid, message = token.validate_access(ip_address, model)

        if is_valid:
            # 更新访问时间
            token.update_access_time()
            await self.db.commit()

        return token, is_valid, message
    
    async def get_token_statistics(self, token_id: int) -> Dict[str, Any]:
        """获取令牌使用情况统计信息。"""
        
        token = await self.get_token_by_id(token_id)
        if not token:
            return {}
        
        # 计算使用百分比
        if token.unlimited_quota:
            usage_percentage = 0.0
        else:
            total_quota = token.remain_quota + token.used_quota
            usage_percentage = (token.used_quota / total_quota * 100) if total_quota > 0 else 0.0
        
        # 计算自创建以来的天数
        days_active = (datetime.now(timezone.utc).replace(tzinfo=None) - token.created_time).days if token.created_time else 0
        
        # 计算每日平均请求数
        avg_requests_per_day = token.request_count / max(1, days_active)
        
        return {
            "token_id": token_id,
            "name": token.name,
            "status": token.status,
            "status_text": token.get_status_text(),
            "remain_quota": token.remain_quota,
            "used_quota": token.used_quota,
            "unlimited_quota": token.unlimited_quota,
            "usage_percentage": usage_percentage,
            "request_count": token.request_count,
            "days_active": days_active,
            "avg_requests_per_day": avg_requests_per_day,
            "last_used": token.last_used_time.isoformat() if token.last_used_time else None,
            "created_time": token.created_time.isoformat() if token.created_time else None,
            "model_limits_enabled": token.model_limits_enabled,
            "model_limits": token.get_model_limits(),
            "ip_restrictions": len(token.get_allowed_ips()) > 0,
            "allowed_ips": token.get_allowed_ips(),
        }
    
    async def regenerate_token_key(self, token_id: int) -> Optional[str]:
        """重新生成令牌 API 密钥。"""
        
        token = await self.get_token_by_id(token_id)
        if not token:
            return None
        
        # 生成新的 API 密钥
        new_key = security.generate_api_key(token.user_id)
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(key=new_key, updated_time=datetime.now(timezone.utc).replace(tzinfo=None))
        )
        await self.db.commit()
        
        logger.info(f"令牌密钥重新生成成功 - ID: {token_id}, 用户ID: {token.user_id}")
        
        return new_key
    
    async def set_token_expiration(self, token_id: int, expired_time: int) -> bool:
        """设置令牌过期时间。"""
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(expired_time=expired_time, updated_time=datetime.now(timezone.utc).replace(tzinfo=None))
        )
        await self.db.commit()
        
        logger.info(f"令牌过期时间设置成功 - ID: {token_id}, 过期时间: {expired_time}")
        
        return True
    
    async def activate_token(self, token_id: int) -> bool:
        """激活令牌。"""
        
        token = await self.get_token_by_id(token_id)
        if not token:
            return False
        
        # 检查令牌是否可以激活
        if token.is_expired():
            return False
        
        if not token.unlimited_quota and token.remain_quota <= 0:
            return False
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(status=1, updated_time=datetime.now(timezone.utc).replace(tzinfo=None))
        )
        await self.db.commit()
        
        logger.info(f"令牌激活成功 - ID: {token_id}")
        return True
    
    async def deactivate_token(self, token_id: int) -> bool:
        """停用令牌。"""
        
        await self.db.execute(
            update(Token)
            .where(Token.id == token_id)
            .values(status=2, updated_time=datetime.now(timezone.utc).replace(tzinfo=None))
        )
        await self.db.commit()
        
        logger.info(f"令牌停用成功 - ID: {token_id}")
        return True
