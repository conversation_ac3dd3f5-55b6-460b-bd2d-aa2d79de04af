#!/usr/bin/env python3
"""
转录 API 功能的简单测试。
"""

import requests
import time
import json
import pytest

# 配置
BASE_URL = "http://localhost:8000"
# 使用时间戳的后 4 位生成短用户名（最多 12 个字符）
timestamp_suffix = str(int(time.time()))[-4:]
USERNAME = f"test{timestamp_suffix}"  # 例如："test1234" (8 个字符)
PASSWORD = "TestPass123!"
EMAIL = f"test{timestamp_suffix}@example.com"

@pytest.mark.acceptance
def test_api():
    print("🧪 Simple Transcription API Test")
    print("=" * 50)
    print(f"Testing user: {USERNAME}")
    
    session = requests.Session()
    session.timeout = 30
    
    try:
        # 测试 1: 注册用户 (不提供电子邮件以避免验证要求)
        print("\n1️⃣ Testing user registration...")
        register_data = {
            "username": USERNAME,
            "password": PASSWORD,
            "email": EMAIL
            # 注意：不提供电子邮件以避免验证要求
        }
        
        response = session.post(f"{BASE_URL}/api/v1/auth/register", json=register_data)
        print(f"   Status: {response.status_code}")
        
        assert response.status_code in [200, 201], f"Registration failed: {response.text}"
        
        print("   ✅ User registered successfully")
        
        # 测试 2: 登录
        print("\n2️⃣ Testing login...")
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }
        
        response = session.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
        print(f"   Status: {response.status_code}")
        
        assert response.status_code == 200, f"Login failed: {response.text}"
        
        data = response.json()
        access_token = data.get("access_token")
        print(f"   ✅ Login successful, token: {access_token[:20]}...")
        
        # 测试 3: 创建 API token
        print("\n3️⃣ Testing API token creation...")
        headers = {"Authorization": f"Bearer {access_token}"}
        token_data = {
            "name": "Test Token",
            "unlimited_quota": True
        }
        
        response = session.post(f"{BASE_URL}/api/v1/tokens", json=token_data, headers=headers)
        print(f"   Status: {response.status_code}")
        
        assert response.status_code == 200, f"Token creation failed: {response.text}"
        
        data = response.json()
        api_key = data.get("key")
        print(f"   ✅ API token created: {api_key[:20]}...")
        
        # 测试 4: 测试 models endpoint
        print("\n4️⃣ Testing models endpoint...")
        headers = {"Authorization": f"Bearer {api_key}"}
        
        response = session.get(f"{BASE_URL}/v1/models", headers=headers)
        print(f"   Status: {response.status_code}")
        
        assert response.status_code == 200, f"Models failed: {response.text}"
        data = response.json()
        models = data.get("data", [])
        print(f"   ✅ Found {len(models)} models")
        for model in models[:3]:
            print(f"      - {model.get('id', 'unknown')}")
        
        # 测试 5: 测试 transcription endpoint
        print("\n5️⃣ Testing transcription endpoint...")
        
        # 创建一个模拟音频文件
        mock_audio_content = b"MOCK_AUDIO_DATA_FOR_TESTING" * 100
        
        files = {
            "file": ("test_audio.mp3", mock_audio_content, "audio/mpeg")
        }
        
        data = {
            "model": "whisper-1",
            "language": "en",
            "response_format": "json"
        }
        
        response = session.post(
            f"{BASE_URL}/v1/audio/transcriptions",
            headers=headers,
            files=files,
            data=data
        )
        
        print(f"   Status: {response.status_code}")
        
        assert response.status_code == 200, f"Transcription failed: {response.text}"
        result = response.json()
        print("   ✅ Transcription successful")
        print(f"      Text: {result.get('text', 'N/A')}")
        print(f"      Language: {result.get('language', 'N/A')}")
        
        # 测试 6: 测试转录列表
        print("\n6️⃣ Testing transcription list...")
        
        response = session.get(f"{BASE_URL}/v1/transcriptions", headers=headers)
        print(f"   Status: {response.status_code}")
        
        assert response.status_code == 200, f"List failed: {response.text}"
        data = response.json()
        tasks = data.get("tasks", [])
        print(f"   ✅ Found {len(tasks)} transcription tasks")
        for task in tasks[:2]:
            print(f"      - {task.get('id', 'unknown')[:8]}... ({task.get('status', 'unknown')})")
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        pytest.fail(f"\n💥 Error during testing: {e}")

if __name__ == "__main__":
    try:
        test_api()
        print("\n✅ Transcription API is working correctly!")
        print(f"💡 Visit {BASE_URL}/docs for API documentation")
    except Exception as e:
        print("\n❌ Some tests failed. Check the application logs.")
